<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class SidebarDropdownMenu extends Component
{
    /**
     * Create a new component instance.
     */

    public $name;
    public $icon;
    public $customIcon;
    public $active = false;
    public $isAddon = false;

    public function __construct($name, $icon, $active, $customIcon = null, $isAddon = false)
    {
        $this->name = $name;
        $this->icon = $this->setIcon($icon);
        $this->active = $active;
        $this->customIcon = $customIcon;
        $this->isAddon = $isAddon;
    }

    public function setIcon($icon)
    {
        switch ($icon) {
        case 'menu':
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-200  dark:group-hover:text-white" height="200px" width="200px" version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve" fill="currentColor"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <style type="text/css"> .st0{fill:currentColor;} </style> <g> <path class="st0" d="M257.783,144.629v60.21c0,3.854-3.271,6.959-7.308,6.959h-1.948c-4.036,0-7.29-3.105-7.29-6.959V144.35 c0-9.916-7.011-12.882-13.708-12.882c-6.715,0-13.709,2.966-13.709,12.882v60.489c0,3.854-3.288,6.959-7.306,6.959h-1.948 c-4.019,0-7.307-3.105-7.307-6.959v-60.21c0-17.763-26.53-17.162-26.53,0.2c0,20.79,0,57.497,0,57.497 c-0.121,31.924,7.863,40.222,21.068,50.164c10.647,8.012,19.746,12.605,19.746,32.498v127.998h31.975V284.988 c0-19.893,9.081-24.486,19.728-32.498c13.205-9.942,21.19-18.24,21.068-50.164c0,0,0-36.708,0-57.497 C284.314,127.467,257.783,126.866,257.783,144.629z"></path> <path class="st0" d="M344.68,150.622c-6.802,18.172-19.536,62.568-19.536,85.115c-1.775,54.235,27.452,25.165,28.183,67.639 v109.966h31.819l0.157,0.392c0,0,0-0.166,0-0.392c0-5.106,0-65.943,0-128.006c0-61.393,0-123.926,0-134.713 C385.303,128.467,355.241,122.361,344.68,150.622z"></path> <path class="st0" d="M475.332,35.481c-4.419-10.448-11.778-19.285-21.05-25.548c-4.627-3.132-9.742-5.61-15.222-7.315 C433.579,0.913,427.768,0,421.766,0H117.111h-4.888h-21.99c-8.002,0-15.692,1.626-22.651,4.567 C57.126,9.002,48.289,16.344,42.026,25.608c-3.132,4.636-5.62,9.751-7.324,15.23c-1.705,5.463-2.609,11.282-2.609,17.258v395.807 c0,7.976,1.618,15.657,4.575,22.615c4.419,10.448,11.778,19.285,21.034,25.548c4.645,3.131,9.776,5.618,15.239,7.315 C78.42,511.087,84.231,512,90.233,512h21.99h4.888h304.655c7.985,0,15.675-1.626,22.633-4.567 c10.456-4.428,19.311-11.769,25.556-21.042c3.131-4.627,5.637-9.751,7.324-15.222c1.723-5.463,2.628-11.282,2.628-17.266V58.096 C479.907,50.129,478.272,42.439,475.332,35.481z M108.186,480.998H90.233c-3.81-0.008-7.341-0.756-10.577-2.122 c-4.837-2.053-9.012-5.506-11.952-9.847c-1.461-2.166-2.61-4.532-3.392-7.072c-0.783-2.531-1.218-5.228-1.218-8.054V58.096 c0-3.775,0.766-7.298,2.122-10.534c2.053-4.828,5.498-9.002,9.847-11.934c2.174-1.462,4.541-2.619,7.08-3.41 c2.54-0.783,5.237-1.21,8.09-1.218h17.954V480.998z M448.888,453.904c0,3.775-0.747,7.298-2.105,10.534 c-2.053,4.836-5.515,9.002-9.847,11.943c-2.174,1.462-4.558,2.61-7.08,3.401c-2.557,0.783-5.236,1.21-8.089,1.218H126.036V31.001 h295.731c3.792,0.009,7.324,0.766,10.56,2.132c4.853,2.044,9.028,5.506,11.952,9.838c1.461,2.166,2.627,4.541,3.41,7.071 c0.783,2.54,1.2,5.22,1.2,8.055V453.904z"></path> </g> </g></svg>';
                break;

        case 'table':
            $this->icon = '<svg fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-200  dark:group-hover:text-white" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 44.999 44.999" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M42.558,23.378l2.406-10.92c0.18-0.816-0.336-1.624-1.152-1.803c-0.816-0.182-1.623,0.335-1.802,1.151l-2.145,9.733 h-9.647c-0.835,0-1.512,0.677-1.512,1.513c0,0.836,0.677,1.513,1.512,1.513h0.573l-3.258,7.713 c-0.325,0.771,0.034,1.657,0.805,1.982c0.19,0.081,0.392,0.12,0.588,0.12c0.59,0,1.15-0.348,1.394-0.925l2.974-7.038l4.717,0.001 l2.971,7.037c0.327,0.77,1.215,1.127,1.982,0.805c0.77-0.325,1.13-1.212,0.805-1.982l-3.257-7.713h0.573 C41.791,24.564,42.403,24.072,42.558,23.378z"></path> <path d="M14.208,24.564h0.573c0.835,0,1.512-0.677,1.512-1.513c0-0.836-0.677-1.513-1.512-1.513H5.134L2.99,11.806 C2.809,10.99,2,10.472,1.188,10.655c-0.815,0.179-1.332,0.987-1.152,1.803l2.406,10.92c0.153,0.693,0.767,1.187,1.477,1.187h0.573 L1.234,32.28c-0.325,0.77,0.035,1.655,0.805,1.98c0.768,0.324,1.656-0.036,1.982-0.805l2.971-7.037l4.717-0.001l2.972,7.038 c0.244,0.577,0.804,0.925,1.394,0.925c0.196,0,0.396-0.039,0.588-0.12c0.77-0.325,1.13-1.212,0.805-1.98L14.208,24.564z"></path> <path d="M24.862,31.353h-0.852V18.308h8.13c0.835,0,1.513-0.677,1.513-1.512s-0.678-1.513-1.513-1.513H12.856 c-0.835,0-1.513,0.678-1.513,1.513c0,0.834,0.678,1.512,1.513,1.512h8.13v13.045h-0.852c-0.835,0-1.512,0.679-1.512,1.514 s0.677,1.513,1.512,1.513h4.728c0.837,0,1.514-0.678,1.514-1.513S25.699,31.353,24.862,31.353z"></path> </g> </g> </g></svg>';
                break;

        case 'payments':
                $this->icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 0 16 16">
          <path d="M12.136.326A1.5 1.5 0 0 1 14 1.78V3h.5A1.5 1.5 0 0 1 16 4.5v9a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 13.5v-9a1.5 1.5 0 0 1 1.432-1.499zM5.562 3H13V1.78a.5.5 0 0 0-.621-.484zM1.5 4a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5z"/>
        </svg>';
                break;

        case 'reports':
                $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" height="24" width="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><g stroke-width="0"/><g stroke-linecap="round" stroke-linejoin="round"/><path d="M80.1 405.6c3.5 2.4 17 9.1 28.4-5.3l77.2-112.8 81.7 78.8c4.3 4.1 10.2 6.3 16.2 5.6 5.9-.6 11.3-3.7 14.8-8.6L433.9 169l4.9 65c1.2 16.5 16.7 19.2 21.9 18.8 11.2-.9 19.7-10.6 18.8-21.9l-8.6-114.3c-.8-11.2-10.6-19.7-21.9-18.8l-114.3 8.6c-11.2.8-19.7 10.6-18.8 21.9.8 11.2 10.5 19.6 21.9 18.8l65-4.9-124.2 178-81.9-79c-4.3-4.2-10.3-6.3-16.2-5.6-6 .6-11.4 3.8-14.8 8.8L74.8 377.2c-6.4 9.3-4 22 5.3 28.4"/><path d="M480.6 460.2H51.8V31.4c0-11.3-9.1-20.4-20.4-20.4S11 20.1 11 31.4v449.2c0 11.3 9.1 20.4 20.4 20.4h449.2c11.3 0 20.4-9.1 20.4-20.4s-9.1-20.4-20.4-20.4"/></svg>';
                break;

        case 'orders':
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" xml:space="preserve"><g stroke-width="0"/><g stroke-linecap="round" stroke-linejoin="round"/><path d="M476.554 371.269H35.446C15.87 371.269 0 387.138 0 406.716c0 19.577 15.87 35.446 35.446 35.446h441.108c19.577 0 35.446-15.869 35.446-35.446 0-19.578-15.869-35.447-35.446-35.447M278.716 133.777c8.1-6.623 13.384-16.561 13.384-27.838 0-19.938-16.161-36.1-36.1-36.1-19.938 0-36.1 16.162-36.1 36.1 0 11.277 5.285 21.216 13.384 27.838-108.954 11.354-193.9 103.47-193.9 215.423h433.231c.001-111.953-84.946-204.069-193.899-215.423M164.908 313.754H94.523c0-70.668 53.2-128.822 121.716-136.823z" style="fill:currentColor"/></svg>';
            break;

        default:
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 -0.5 25 25" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M9.41728 18.9999C9.41728 19.4142 9.75307 19.7499 10.1673 19.7499C10.5815 19.7499 10.9173 19.4142 10.9173 18.9999H9.41728ZM10.1673 16.6669H9.41728H10.1673ZM14.0853 18.9999C14.0853 19.4142 14.4211 19.7499 14.8353 19.7499C15.2495 19.7499 15.5853 19.4142 15.5853 18.9999H14.0853ZM10.1673 19.7499C10.5815 19.7499 10.9173 19.4142 10.9173 18.9999C10.9173 18.5857 10.5815 18.2499 10.1673 18.2499V19.7499ZM7.83328 18.9999L7.82564 19.7499H7.83328V18.9999ZM5.80128 17.2529L5.0518 17.2807C5.05294 17.3116 5.056 17.3424 5.06095 17.373L5.80128 17.2529ZM5.53228 9.99395L6.28177 9.96617C6.2805 9.93188 6.27687 9.8977 6.27092 9.8639L5.53228 9.99395ZM6.64428 7.74195L6.3033 7.07392L6.29848 7.07642L6.64428 7.74195ZM11.5793 5.22295L11.9203 5.89096L11.9218 5.89017L11.5793 5.22295ZM13.4243 5.22295L13.0818 5.89017L13.0833 5.89096L13.4243 5.22295ZM18.3593 7.74195L18.7051 7.07641L18.7003 7.07394L18.3593 7.74195ZM19.4713 9.99395L18.7326 9.8639C18.7267 9.89767 18.7231 9.93181 18.7218 9.96607L19.4713 9.99395ZM19.2013 17.2529L19.9416 17.373C19.9466 17.3425 19.9496 17.3117 19.9508 17.2808L19.2013 17.2529ZM17.1693 18.9999V19.75L17.1769 19.7499L17.1693 18.9999ZM14.8353 18.2499C14.4211 18.2499 14.0853 18.5857 14.0853 18.9999C14.0853 19.4142 14.4211 19.7499 14.8353 19.7499V18.2499ZM10.1673 18.2499C9.75307 18.2499 9.41728 18.5857 9.41728 18.9999C9.41728 19.4142 9.75307 19.7499 10.1673 19.7499V18.2499ZM14.8353 19.7499C15.2495 19.7499 15.5853 19.4142 15.5853 18.9999C15.5853 18.5857 15.2495 18.2499 14.8353 18.2499V19.7499ZM10.9173 18.9999V16.6669H9.41728V18.9999H10.9173ZM10.9173 16.6669C10.9173 15.7921 11.6265 15.0829 12.5013 15.0829V13.5829C10.798 13.5829 9.41728 14.9637 9.41728 16.6669H10.9173ZM12.5013 15.0829C13.3761 15.0829 14.0853 15.7921 14.0853 16.6669H15.5853C15.5853 14.9637 14.2045 13.5829 12.5013 13.5829V15.0829ZM14.0853 16.6669V18.9999H15.5853V16.6669H14.0853ZM10.1673 18.2499H7.83328V19.7499H10.1673V18.2499ZM7.84092 18.25C7.1937 18.2434 6.64521 17.7718 6.54162 17.1329L5.06095 17.373C5.28137 18.7325 6.44847 19.7359 7.82564 19.7499L7.84092 18.25ZM6.55077 17.2252L6.28177 9.96617L4.7828 10.0217L5.0518 17.2807L6.55077 17.2252ZM6.27092 9.8639C6.16697 9.27348 6.45811 8.68388 6.99008 8.40747L6.29848 7.07642C5.18533 7.65481 4.57613 8.88855 4.79364 10.124L6.27092 9.8639ZM6.98526 8.40996L11.9203 5.89096L11.2383 4.55494L6.30331 7.07394L6.98526 8.40996ZM11.9218 5.89017C12.2859 5.70328 12.7177 5.70328 13.0818 5.89017L13.7668 4.55573C12.9727 4.14809 12.0309 4.14809 11.2368 4.55573L11.9218 5.89017ZM13.0833 5.89096L18.0183 8.40996L18.7003 7.07394L13.7653 4.55494L13.0833 5.89096ZM18.0135 8.40747C18.5455 8.68388 18.8366 9.27348 18.7326 9.8639L20.2099 10.124C20.4274 8.88855 19.8182 7.65481 18.7051 7.07642L18.0135 8.40747ZM18.7218 9.96607L18.4518 17.2251L19.9508 17.2808L20.2208 10.0218L18.7218 9.96607ZM18.461 17.1329C18.3574 17.7718 17.8089 18.2434 17.1616 18.25L17.1769 19.7499C18.5541 19.7359 19.7212 18.7325 19.9416 17.373L18.461 17.1329ZM17.1693 18.2499H14.8353V19.7499H17.1693V18.2499ZM10.1673 19.7499H14.8353V18.2499H10.1673V19.7499Z" fill="currentColor"></path> </g></svg>';
                break;
        }

        return $this->icon;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.sidebar-dropdown-menu');
    }

}
