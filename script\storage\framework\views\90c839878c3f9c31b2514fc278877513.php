<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(isRtl() ? 'rtl' : 'ltr'); ?>">

<head>
    <link rel="manifest" href="<?php echo e(url('manifest.json')); ?>?url=<?php echo e(urlencode(ltrim(Request::getRequestUri(), '/'))); ?>&hash=<?php echo e($restaurant->hash); ?>" crossorigin="use-credentials">
    <meta name="theme-color" content="#ffffff">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <?php
    function getFaviconPath($size, $restaurant) {
        $fileName = match($size) {
            '180' => 'apple-touch-icon.png',
            '192' => 'android-chrome-192x192.png',
            '512' => 'android-chrome-512x512.png',
            '16' => 'favicon-16x16.png',
            '32' => 'favicon-32x32.png',
            'ico' => 'favicon.ico'
        };
        
        $restaurantPath = "user-uploads/favicons/restaurant/{$restaurant->hash}/{$fileName}";
        $defaultPath = "user-uploads/favicons/super-admin/{$fileName}";
        
        return File::exists(public_path($restaurantPath)) 
            ? asset($restaurantPath) 
            : asset($defaultPath);
    }
    ?>

    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(getFaviconPath('180', $restaurant)); ?>">
    <link rel="icon" type="image/png" sizes="192x192" href="<?php echo e(getFaviconPath('192', $restaurant)); ?>">
    <link rel="icon" type="image/png" sizes="512x512" href="<?php echo e(getFaviconPath('512', $restaurant)); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(getFaviconPath('16', $restaurant)); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(getFaviconPath('32', $restaurant)); ?>">
    <link rel="shortcut icon" href="<?php echo e(getFaviconPath('ico', $restaurant)); ?>">

    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="<?php echo e($restaurant->logoUrl); ?>">

    
    <meta name="keyword" content="<?php echo e($restaurant->meta_keyword ?? ''); ?>">
    <meta name="description" content="<?php echo e($restaurant->meta_description ?? $restaurant->name); ?>">
    <title><?php echo e($restaurant->name); ?></title>

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Styles -->
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <style>
        :root {
            /* --color-base: 219, 41, 41; */
            --color-base: <?php echo e($restaurant->theme_rgb); ?>;
            --livewire-progress-bar-color: <?php echo e($restaurant->theme_hex); ?>;
        }
    </style>

    <?php if(File::exists(public_path() . '/css/app-custom.css')): ?>
        <link href="<?php echo e(asset('css/app-custom.css')); ?>" rel="stylesheet">
    <?php endif; ?>

    
    <?php if ($__env->exists('sections.custom_script_customer')) echo $__env->make('sections.custom_script_customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

</head>

<body class="font-sans antialiased dark:bg-gray-900">

    <div class="mx-auto max-w-lg lg:max-w-screen-xl min-h-svh shadow-md lg:shadow-none">
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shopNavigation', ['restaurant' => $restaurant, 'shopBranch' => $shopBranch]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1458745590-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shopDesktopNavigation', ['restaurant' => $restaurant, 'shopBranch' => $shopBranch]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1458745590-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <div class="flex mt-4 overflow-hidden  dark:bg-gray-900">
            <div id="main-content" class="w-full h-full overflow-y-auto dark:bg-gray-900">
                <main>
                    <?php echo $__env->yieldContent('content'); ?>

                    <?php echo e($slot ?? ''); ?>

                </main>
            </div>
        </div>

    </div>
    <?php echo $__env->yieldPushContent('modals'); ?>

    <footer class="p-4 bg-white sm:p-6 dark:bg-gray-800 border-t dark:border-gray-600">
        <div class="mx-auto max-w-screen-xl">
            <div class="sm:flex sm:items-center sm:justify-between">
                <span class="text-sm text-gray-500 sm:text-center dark:text-gray-400">&copy; <?php echo e(now()->year); ?> <a
                        href="" class="hover:underline"><?php echo e($restaurant->name); ?></a>. <?php echo app('translator')->get('app.allRightsReserved'); ?>
                </span>
                <div class="flex mt-4 space-x-6 sm:justify-center sm:mt-0 rtl:space-x-reverse">
                    <?php if(languages()->count() > 1): ?>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shop.languageSwitcher');

$__html = app('livewire')->mount($__name, $__params, 'lw-1458745590-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <?php endif; ?>

                    <?php if($restaurant->facebook_link): ?>
                        <a href="<?php echo e($restaurant->facebook_link); ?>"
                            class="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                    <?php endif; ?>
                    <?php if($restaurant->instagram_link): ?>
                        <a href="<?php echo e($restaurant->instagram_link); ?>"
                            class="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                    <?php endif; ?>
                    <?php if($restaurant->twitter_link): ?>
                        <a href="<?php echo e($restaurant->twitter_link); ?>"
                            class="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 50 50 " aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100"
                                viewBox="0 0 50 50">
                                <path
                                    d="M 6.9199219 6 L 21.136719 26.726562 L 6.2285156 44 L 9.40625 44 L 22.544922 28.777344 L 32.986328 44 L 43 44 L 28.123047 22.3125 L 42.203125 6 L 39.027344 6 L 26.716797 20.261719 L 16.933594 6 L 6.9199219 6 z">
                                </path>
                            </svg>
                        </a>
                    <?php endif; ?>
                    <?php if($restaurant->yelp_link): ?>
                        <a href="<?php echo e($restaurant->yelp_link); ?>"
                            class="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 30 30" aria-hidden="true">
                                <path
                                    d="M13.961 22.279c0.246-0.273 0.601-0.444 0.995-0.444 0.739 0 1.338 0.599 1.338 1.338 0 0.016-0 0.032-0.001 0.048l0-0.002-0.237 6.483c-0.027 0.719-0.616 1.293-1.34 1.293-0.077 0-0.153-0.006-0.226-0.019l0.008 0.001c-1.763-0.303-3.331-0.962-4.69-1.902l0.039 0.025c-0.351-0.245-0.578-0.647-0.578-1.102 0-0.346 0.131-0.661 0.346-0.898l-0.001 0.001 4.345-4.829zM12.853 20.434l-6.301 1.572c-0.097 0.025-0.208 0.039-0.322 0.039-0.687 0-1.253-0.517-1.332-1.183l-0.001-0.006c-0.046-0.389-0.073-0.839-0.073-1.295 0-1.324 0.223-2.597 0.635-3.781l-0.024 0.081c0.183-0.534 0.681-0.911 1.267-0.911 0.214 0 0.417 0.050 0.596 0.14l-0.008-0.004 5.833 2.848c0.45 0.221 0.754 0.677 0.754 1.203 0 0.623-0.427 1.147-1.004 1.294l-0.009 0.002zM13.924 15.223l-6.104-10.574c-0.112-0.191-0.178-0.421-0.178-0.667 0-0.529 0.307-0.987 0.752-1.204l0.008-0.003c1.918-0.938 4.153-1.568 6.511-1.761l0.067-0.004c0.031-0.003 0.067-0.004 0.104-0.004 0.738 0 1.337 0.599 1.337 1.337 0 0.001 0 0.001 0 0.002v-0 12.207c-0 0.739-0.599 1.338-1.338 1.338-0.493 0-0.923-0.266-1.155-0.663l-0.003-0.006zM19.918 20.681l6.176 2.007c0.541 0.18 0.925 0.682 0.925 1.274 0 0.209-0.048 0.407-0.134 0.584l0.003-0.008c-0.758 1.569-1.799 2.889-3.068 3.945l-0.019 0.015c-0.23 0.19-0.527 0.306-0.852 0.306-0.477 0-0.896-0.249-1.134-0.625l-0.003-0.006-3.449-5.51c-0.128-0.201-0.203-0.446-0.203-0.709 0-0.738 0.598-1.336 1.336-1.336 0.147 0 0.289 0.024 0.421 0.068l-0.009-0.003zM26.197 16.742l-6.242 1.791c-0.11 0.033-0.237 0.052-0.368 0.052-0.737 0-1.335-0.598-1.335-1.335 0-0.282 0.087-0.543 0.236-0.758l-0.003 0.004 3.63-5.383c0.244-0.358 0.65-0.59 1.111-0.59 0.339 0 0.649 0.126 0.885 0.334l-0.001-0.001c1.25 1.104 2.25 2.459 2.925 3.99l0.029 0.073c0.070 0.158 0.111 0.342 0.111 0.535 0 0.608-0.405 1.121-0.959 1.286l-0.009 0.002z">
                                </path>
                            </svg> </a>
                    <?php endif; ?>



                </div>
            </div>
        </div>
    </footer>


    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>



    <?php echo $__env->make('layouts.update-uri', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <script src="<?php echo e(asset('vendor/livewire-alert/livewire-alert.js')); ?>" defer data-navigate-track></script>

    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

    <script src="https://js.stripe.com/v3/"></script>

    <form action="<?php echo e(route('stripe.order_payment')); ?>" method="POST" id="order-payment-form" class="hidden">
        <?php echo csrf_field(); ?>

        <input type="hidden" id="order_payment" name="order_payment">

        <div class="form-row">
            <label for="card-element">
                Credit or debit card
            </label>
            <div id="card-element">
                <!-- A Stripe Element will be inserted here. -->
            </div>

            <!-- Used to display Element errors. -->
            <div id="card-errors" role="alert"></div>
        </div>

        <button>Submit Payment</button>
    </form>

    <?php if($restaurant->paymentGateways->stripe_status): ?>
        <script>
            const stripe = Stripe('<?php echo e($restaurant->paymentGateways->stripe_key); ?>');
            const elements = stripe.elements({
                currency: '<?php echo e(strtolower($restaurant->currency->currency_code)); ?>',
            });
        </script>
    <?php endif; ?>
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('<?php echo e(asset('service-worker.js')); ?>')
                    .then(registration => {
                        console.log('Service Worker registered:', registration);
                    })
                    .catch(error => {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }
        self.addEventListener("fetch", (event) => {
            if (event.request.mode === "navigate") {
                event.respondWith(
                    fetch(event.request.url)
                );
            }
        });
    </script>

    <script>
        <?php if($restaurant->is_pwa_install_alert_show == 1): ?>
            var isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
            var isInStandaloneMode = ('standalone' in window.navigator) && window.navigator.standalone;
            var deferredPrompt;

            window.addEventListener("beforeinstallprompt", (event) => {
                event.preventDefault(); // Prevent default install prompt
                deferredPrompt = event; // Store the event for later use

                // Prevent showing again if user has dismissed in this tab
                if (!sessionStorage.getItem("pwaDismissed")) {
                    ['scroll', 'click'].forEach(evt => {
                        window.addEventListener(evt, showInstallPrompt, { once: true });
                    });
                }
            });

            function showInstallPrompt() {
                if (deferredPrompt) {
                    deferredPrompt.prompt(); // Show the install prompt

                    deferredPrompt.userChoice.then(({ outcome }) => {
                        console.log(`User ${outcome === 'accepted' ? 'accepted' : 'dismissed'} the PWA install`);

                        if (outcome === 'dismissed') {
                            sessionStorage.setItem("pwaDismissed", "true"); // Prevent showing again in this session
                        }

                        deferredPrompt = null;
                    });
                }
            }

            // Handle iOS specific add to home screen prompt
            if (isIOS && !isInStandaloneMode) {
                // Check if prompt was shown in last 24 hours
                const lastPrompt = localStorage.getItem('iosPromptLastShown');
                const now = new Date().getTime();

                if (!lastPrompt || (now - parseInt(lastPrompt)) > 24 * 60 * 60 * 1000) {
                    ['scroll', 'click'].forEach(evt => {
                        window.addEventListener(evt, showIOSInstallInstructions, { once: true });
                    });
                }
            }

            function showIOSInstallInstructions() {
                if (document.getElementById('iosInstallInstructions')) return;

                // Store the current timestamp when showing the prompt
                localStorage.setItem('iosPromptLastShown', new Date().getTime());

                const instructions = document.createElement('div');
                instructions.id = 'iosInstallInstructions';
                instructions.innerHTML = `
                    <div style="position: fixed; bottom: 10px; left: 10px; right: 10px; background: #fff; padding:
                 <div style="position: fixed; bottom: 10px; left: 10px; right: 10px; background: #fff; padding: 10px; border: 1px solid #ccc; border-radius: 5px; text-align: center; z-index: 1000;">
                    <p class="flex relative"><?php echo app('translator')->get('messages.installAppInstruction'); ?>
                        <img class="absolute right-0 left-auto mr-5" src="<?php echo e(asset('img/share-ios.svg')); ?>" alt="Share Icon" style="width: 20px; vertical-align: middle;">

                    </p>
                    <?php echo app('translator')->get('messages.addToHomeScreen'); ?>.
                    <button id="closeInstructions" class="block text-center mx-auto" style="margin-top: 10px; padding: 5px 10px;"><?php echo app('translator')->get('app.close'); ?></button>
                </div>
            `;
            document.body.appendChild(instructions);

            // Add click event to close button
            document.getElementById('closeInstructions').addEventListener('click', function () {
                document.getElementById('iosInstallInstructions').remove();
            });
        }

    <?php endif; ?>

</script>

    <?php if (isset($component)) { $__componentOriginald2d87b894a350bded0052b294742bbb9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald2d87b894a350bded0052b294742bbb9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'livewire-alert::components.flash','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('livewire-alert::flash'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald2d87b894a350bded0052b294742bbb9)): ?>
<?php $attributes = $__attributesOriginald2d87b894a350bded0052b294742bbb9; ?>
<?php unset($__attributesOriginald2d87b894a350bded0052b294742bbb9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald2d87b894a350bded0052b294742bbb9)): ?>
<?php $component = $__componentOriginald2d87b894a350bded0052b294742bbb9; ?>
<?php unset($__componentOriginald2d87b894a350bded0052b294742bbb9); ?>
<?php endif; ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Downloads\TableTrack v1.2.33 Nulled\TableTrack v1.2.33 Nulled\script\resources\views/layouts/guest.blade.php ENDPATH**/ ?>