9999999999O:24:"App\Models\GlobalSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"global_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:50:{s:2:"id";i:1;s:13:"purchase_code";N;s:15:"supported_until";N;s:24:"last_license_verified_at";N;s:5:"email";N;s:10:"created_at";s:19:"2025-06-29 17:42:55";s:10:"updated_at";s:19:"2025-06-29 17:42:55";s:4:"name";s:10:"TableTrack";s:4:"logo";N;s:9:"theme_hex";s:7:"#A78BFA";s:9:"theme_rgb";s:13:"167, 139, 250";s:6:"locale";s:2:"en";s:12:"license_type";N;s:13:"hide_cron_job";i:0;s:13:"last_cron_run";N;s:13:"system_update";i:1;s:12:"purchased_on";N;s:8:"timezone";s:12:"Asia/Kolkata";s:20:"disable_landing_site";i:0;s:12:"landing_type";s:6:"static";s:17:"landing_site_type";s:5:"theme";s:16:"landing_site_url";N;s:13:"installed_url";s:16:"http://localhost";s:30:"requires_approval_after_signup";i:0;s:13:"facebook_link";s:25:"https://www.facebook.com/";s:14:"instagram_link";s:26:"https://www.instagram.com/";s:12:"twitter_link";s:24:"https://www.twitter.com/";s:9:"yelp_link";N;s:19:"default_currency_id";i:1;s:14:"show_logo_text";i:1;s:10:"meta_title";N;s:12:"meta_keyword";N;s:16:"meta_description";N;s:34:"upload_fav_icon_android_chrome_192";N;s:34:"upload_fav_icon_android_chrome_512";N;s:32:"upload_fav_icon_apple_touch_icon";N;s:17:"upload_favicon_16";N;s:17:"upload_favicon_32";N;s:7:"favicon";N;s:4:"hash";s:32:"0bf06b78371c76ba1a571b251a05f700";s:11:"webmanifest";N;s:25:"is_pwa_install_alert_show";i:0;s:18:"google_map_api_key";N;s:14:"session_driver";s:8:"database";s:13:"enable_stripe";i:1;s:15:"enable_razorpay";i:1;s:18:"enable_flutterwave";i:1;s:14:"enable_payfast";i:1;s:13:"enable_paypal";i:1;s:15:"enable_paystack";i:1;}s:11:" * original";a:50:{s:2:"id";i:1;s:13:"purchase_code";N;s:15:"supported_until";N;s:24:"last_license_verified_at";N;s:5:"email";N;s:10:"created_at";s:19:"2025-06-29 17:42:55";s:10:"updated_at";s:19:"2025-06-29 17:42:55";s:4:"name";s:10:"TableTrack";s:4:"logo";N;s:9:"theme_hex";s:7:"#A78BFA";s:9:"theme_rgb";s:13:"167, 139, 250";s:6:"locale";s:2:"en";s:12:"license_type";N;s:13:"hide_cron_job";i:0;s:13:"last_cron_run";N;s:13:"system_update";i:1;s:12:"purchased_on";N;s:8:"timezone";s:12:"Asia/Kolkata";s:20:"disable_landing_site";i:0;s:12:"landing_type";s:6:"static";s:17:"landing_site_type";s:5:"theme";s:16:"landing_site_url";N;s:13:"installed_url";s:16:"http://localhost";s:30:"requires_approval_after_signup";i:0;s:13:"facebook_link";s:25:"https://www.facebook.com/";s:14:"instagram_link";s:26:"https://www.instagram.com/";s:12:"twitter_link";s:24:"https://www.twitter.com/";s:9:"yelp_link";N;s:19:"default_currency_id";i:1;s:14:"show_logo_text";i:1;s:10:"meta_title";N;s:12:"meta_keyword";N;s:16:"meta_description";N;s:34:"upload_fav_icon_android_chrome_192";N;s:34:"upload_fav_icon_android_chrome_512";N;s:32:"upload_fav_icon_apple_touch_icon";N;s:17:"upload_favicon_16";N;s:17:"upload_favicon_32";N;s:7:"favicon";N;s:4:"hash";s:32:"0bf06b78371c76ba1a571b251a05f700";s:11:"webmanifest";N;s:25:"is_pwa_install_alert_show";s:1:"0";s:18:"google_map_api_key";N;s:14:"session_driver";s:8:"database";s:13:"enable_stripe";i:1;s:15:"enable_razorpay";i:1;s:18:"enable_flutterwave";i:1;s:14:"enable_payfast";i:1;s:13:"enable_paypal";i:1;s:15:"enable_paystack";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:4:{s:12:"purchased_on";s:8:"datetime";s:15:"supported_until";s:8:"datetime";s:24:"last_license_verified_at";s:8:"datetime";s:13:"last_cron_run";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:8:"logo_url";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}}