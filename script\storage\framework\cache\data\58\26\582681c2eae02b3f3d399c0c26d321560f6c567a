9999999999O:18:"App\Models\Package":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"packages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:33:{s:2:"id";i:1;s:12:"package_name";s:7:"Default";s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-06-29 17:42:55";s:10:"updated_at";s:19:"2025-06-29 17:42:55";s:11:"currency_id";i:1;s:11:"description";s:43:"Its a default package and cannot be deleted";s:12:"annual_price";N;s:13:"monthly_price";N;s:14:"monthly_status";i:0;s:13:"annual_status";i:0;s:21:"stripe_annual_plan_id";N;s:22:"stripe_monthly_plan_id";N;s:23:"razorpay_annual_plan_id";N;s:24:"razorpay_monthly_plan_id";N;s:26:"flutterwave_annual_plan_id";N;s:27:"flutterwave_monthly_plan_id";N;s:23:"paystack_annual_plan_id";N;s:24:"paystack_monthly_plan_id";N;s:23:"stripe_lifetime_plan_id";N;s:25:"razorpay_lifetime_plan_id";N;s:13:"billing_cycle";i:12;s:10:"sort_order";i:1;s:10:"is_private";i:0;s:7:"is_free";i:1;s:14:"is_recommended";i:0;s:12:"package_type";s:7:"default";s:12:"trial_status";N;s:10:"trial_days";N;s:30:"trial_notification_before_days";N;s:13:"trial_message";N;s:19:"additional_features";N;s:12:"branch_limit";N;}s:11:" * original";a:33:{s:2:"id";i:1;s:12:"package_name";s:7:"Default";s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-06-29 17:42:55";s:10:"updated_at";s:19:"2025-06-29 17:42:55";s:11:"currency_id";i:1;s:11:"description";s:43:"Its a default package and cannot be deleted";s:12:"annual_price";N;s:13:"monthly_price";N;s:14:"monthly_status";s:1:"0";s:13:"annual_status";s:1:"0";s:21:"stripe_annual_plan_id";N;s:22:"stripe_monthly_plan_id";N;s:23:"razorpay_annual_plan_id";N;s:24:"razorpay_monthly_plan_id";N;s:26:"flutterwave_annual_plan_id";N;s:27:"flutterwave_monthly_plan_id";N;s:23:"paystack_annual_plan_id";N;s:24:"paystack_monthly_plan_id";N;s:23:"stripe_lifetime_plan_id";N;s:25:"razorpay_lifetime_plan_id";N;s:13:"billing_cycle";i:12;s:10:"sort_order";i:1;s:10:"is_private";i:0;s:7:"is_free";i:1;s:14:"is_recommended";i:0;s:12:"package_type";s:7:"default";s:12:"trial_status";N;s:10:"trial_days";N;s:30:"trial_notification_before_days";N;s:13:"trial_message";N;s:19:"additional_features";N;s:12:"branch_limit";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:12:"package_type";s:21:"App\Enums\PackageType";s:10:"trial_days";s:7:"integer";s:30:"trial_notification_before_days";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}}