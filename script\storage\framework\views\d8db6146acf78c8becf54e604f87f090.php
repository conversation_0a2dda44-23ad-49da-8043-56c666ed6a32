<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(isRtl() ? 'rtl' : 'ltr'); ?>">

<head>
   <?php
        $lastSegment = last(request()->segments());
    ?>
    <?php if(user()->restaurant_id): ?>
        <link rel="manifest" href="<?php echo e(asset('manifest.json')); ?><?php if($lastSegment): ?>?url=<?php echo e($lastSegment); ?>&hash=<?php echo e(user()->restaurant->hash); ?><?php endif; ?>" crossorigin="use-credentials">
    <?php else: ?>
        <link rel="manifest" href="<?php echo e(asset('manifest.json')); ?><?php if($lastSegment): ?>?url=<?php echo e($lastSegment); ?><?php endif; ?>" crossorigin="use-credentials">
    <?php endif; ?>
    <meta name="theme-color" content="#ffffff">
    <meta name="description" content="<?php echo e(global_setting()->name); ?>">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/trix/1.3.1/trix.min.css" />


    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(restaurantOrGlobalSetting()->upload_fav_icon_apple_touch_icon_url); ?>">
    <link rel="icon" type="image/png" sizes="192x192" href="<?php echo e(restaurantOrGlobalSetting()->upload_fav_icon_android_chrome_192_url); ?>">
    <link rel="icon" type="image/png" sizes="512x512" href="<?php echo e(restaurantOrGlobalSetting()->upload_fav_icon_android_chrome_512_url); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(restaurantOrGlobalSetting()->upload_favicon_16_url); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(restaurantOrGlobalSetting()->upload_favicon_32_url); ?>">
    <link rel="shortcut icon" href="<?php echo e(restaurantOrGlobalSetting()->favicon_url); ?>">


    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="<?php echo e(global_setting()->logoUrl); ?>">

    <title><?php echo e(global_setting()->name); ?></title>

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Styles -->
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <?php echo $__env->yieldPushContent('styles'); ?>

    <?php echo $__env->make('sections.theme_style', [
        'baseColor' => restaurantOrGlobalSetting()->theme_rgb,
        'baseColorHex' => restaurantOrGlobalSetting()->theme_hex,
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


    <?php if(File::exists(public_path() . '/css/app-custom.css')): ?>
        <link href="<?php echo e(asset('css/app-custom.css')); ?>" rel="stylesheet">
    <?php endif; ?>


    <script src="https://js.pusher.com/beams/2.1.0/push-notifications-cdn.js" async></script>

    <script>
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia(
                '(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark')
        } else {
            document.documentElement.classList.remove('dark')
        }
    </script>

    <script>
        if (localStorage.getItem("menu-collapsed") === "true") {
            document.documentElement.style.visibility = 'hidden';
            window.addEventListener('DOMContentLoaded', () => {
                const sidebar = document.getElementById('sidebar');
                const openIcon = document.getElementById('toggle-sidebar-open');
                const closeIcon = document.getElementById('toggle-sidebar-close');

                if (sidebar) {
                    sidebar.classList.add('hidden');
                    sidebar.classList.remove('flex', 'lg:flex');
                }

                if (openIcon && closeIcon) {
                    openIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');
                }

                setTimeout(() => {
                    document.documentElement.style.visibility = 'visible';
                }, 50);
            });
        } else {
            // Handle expanded state icons without hiding the page
            window.addEventListener('DOMContentLoaded', () => {
                const openIcon = document.getElementById('toggle-sidebar-open');
                const closeIcon = document.getElementById('toggle-sidebar-close');

                if (openIcon && closeIcon) {
                    openIcon.classList.add('hidden');
                    closeIcon.classList.remove('hidden');
                }
            });
        }
    </script>

    
    <?php if ($__env->exists('sections.custom_script_admin')) echo $__env->make('sections.custom_script_admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</head>


<body class="font-sans antialiased dark:bg-gray-900" id="main-body">

    <?php if(user()->restaurant_id): ?>
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('navigation-menu');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
    <?php else: ?>
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('superadmin-navigation-menu');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
    <?php endif; ?>

    <div class="flex rtl:flex-row-reverse pt-16 overflow-hidden bg-gray-50 dark:bg-gray-900 h-screen">

        <?php if(user()->restaurant_id): ?>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('sidebar');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        <?php else: ?>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('superadmin-sidebar');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-3', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        <?php endif; ?>


        <div id="main-content"
            class="relative w-full h-full overflow-y-auto bg-gray-50 ltr:lg:ml-64 rtl:lg:mr-64 dark:bg-gray-900">
            <main>
                <?php echo $__env->yieldContent('content'); ?>
                <?php echo e($slot ?? ''); ?>

            </main>


        </div>


    </div>

    <?php echo $__env->yieldPushContent('modals'); ?>


    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <?php echo $__env->make('layouts.update-uri', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <script src="<?php echo e(asset('vendor/livewire-alert/livewire-alert.js')); ?>" defer data-navigate-track></script>
    <?php if (isset($component)) { $__componentOriginald2d87b894a350bded0052b294742bbb9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald2d87b894a350bded0052b294742bbb9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'livewire-alert::components.flash','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('livewire-alert::flash'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald2d87b894a350bded0052b294742bbb9)): ?>
<?php $attributes = $__attributesOriginald2d87b894a350bded0052b294742bbb9; ?>
<?php unset($__attributesOriginald2d87b894a350bded0052b294742bbb9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald2d87b894a350bded0052b294742bbb9)): ?>
<?php $component = $__componentOriginald2d87b894a350bded0052b294742bbb9; ?>
<?php unset($__componentOriginald2d87b894a350bded0052b294742bbb9); ?>
<?php endif; ?>

    <?php if(user()->restaurant_id): ?>
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('order.OrderDetail');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-4', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('customer.addCustomer');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-5', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.upgradeLicense');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-6', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('order.addPayment');

$__html = app('livewire')->mount($__name, $__params, 'lw-**********-7', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>


        <script src="https://js.stripe.com/v3/"></script>

        <form action="<?php echo e(route('stripe.license_payment')); ?>" method="POST" id="license-payment-form" class="hidden">
            <?php echo csrf_field(); ?>

            <input type="hidden" id="license_payment" name="license_payment">
            <input type="hidden" id="package_type" name="package_type">
            <input type="hidden" id="package_id" name="package_id">
            <input type="hidden" id="currency_id" name="currency_id">

            <div class="form-row">
                <label for="card-element">
                    Credit or debit card
                </label>
                <div id="card-element">
                    <!-- A Stripe Element will be inserted here. -->
                </div>

                <!-- Used to display Element errors. -->
                <div id="card-errors" role="alert"></div>
            </div>

            <button>Submit Payment</button>
        </form>

        <?php if(superadminPaymentGateway()->stripe_status): ?>
            <script>
                const stripe = Stripe('<?php echo e(superadminPaymentGateway()->stripe_key); ?>');
                const elements = stripe.elements({
                    currency: '<?php echo e(strtolower(restaurant()->currency->currency_code)); ?>',
                });
            </script>
        <?php endif; ?>

        <?php if(superadminPaymentGateway()->flutterwave_status): ?>
            <script src="https://checkout.flutterwave.com/v3.js"></script>
            <form action="<?php echo e(route('flutterwave.initiate-payment')); ?>" method="POST" id="flutterwavePaymentformNew" class="hidden">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="payment_id">
                <input type="hidden" name="amount">
                <input type="hidden" name="currency">
                <input type="hidden" name="restaurant_id">
                <input type="hidden" name="package_id">
                <input type="hidden" name="package_type">
            </form>
        <?php endif; ?>

        <?php if(superadminPaymentGateway()->paypal_status): ?>
            <script src="https://www.paypal.com/sdk/js?client-id=<?php echo e(superadminPaymentGateway()->paypal_client_id); ?>&currency=<?php echo e(restaurant()->currency->currency_code); ?>"></script>
            <form action="<?php echo e(route('paypal.initiate-payment')); ?>" method="POST" id="paypalPaymentForm" class="hidden">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="payment_id">
                <input type="hidden" name="amount">
                <input type="hidden" name="currency">
                <input type="hidden" name="restaurant_id">
                <input type="hidden" name="package_id">
                <input type="hidden" name="package_type">
            </form>
        <?php endif; ?>

        <?php if(superadminPaymentGateway()->payfast_status): ?>
            <form action="<?php echo e(route('payfast.initiate-payment')); ?>" method="POST" id="payfastPaymentForm" class="hidden">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="payment_id">
                <input type="hidden" name="amount">
                <input type="hidden" name="currency">
                <input type="hidden" name="restaurant_id">
                <input type="hidden" name="package_id">
                <input type="hidden" name="package_type">
            </form>
        <?php endif; ?>

        <?php if(superadminPaymentGateway()->paystack_status): ?>
            <script src="https://js.paystack.co/v1/inline.js"></script>
            <form action="<?php echo e(route('paystack.initiate-payment')); ?>" method="POST" id="paystackPaymentformNew" class="hidden">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="payment_id">
                <input type="hidden" name="amount">
                <input type="hidden" name="currency">
                <input type="hidden" name="restaurant_id">
                <input type="hidden" name="package_id">
                <input type="hidden" name="package_type">
                <input type="hidden" name="email">
            </form>
        <?php endif; ?>

    <?php endif; ?>


    <?php if(App::environment('codecanyon') && pusherSettings()->beamer_status): ?>
        <script>
            const currentUserId = "<?php echo e(Str::slug(global_setting()->name)); ?>-<?php echo e(auth()->id()); ?>"; // Get this from your auth system

            const beamsClient = new PusherPushNotifications.Client({
                instanceId: "<?php echo e(pusherSettings()->instance_id); ?>",
            });

            const beamsTokenProvider = new PusherPushNotifications.TokenProvider({
                url: "<?php echo e(route('beam_auth')); ?>",
            });

            beamsClient.start()
                .then(() => beamsClient.addDeviceInterest('<?php echo e(Str::slug(global_setting()->name)); ?>'))
                .then(() => beamsClient.setUserId(currentUserId, beamsTokenProvider))
                .then(() => console.log('Successfully registered and subscribed!'))
                .catch(console.error);

            beamsClient
                .getUserId()
                .then((userId) => {
                    console.log(userId, currentUserId);
                    // Check if the Beams user matches the user that is currently logged in
                    if (userId !== currentUserId) {
                        // Unregister for notifications
                        return beamsClient.stop();
                    }
                })
                .catch(console.error);
        </script>
    <?php endif; ?>

    <script>
        var elem = document.getElementById("main-body");

        function openFullscreen() {
            if (!document.fullscreenElement) {
                if (elem.requestFullscreen) {
                    elem.requestFullscreen();
                } else if (elem.webkitRequestFullscreen) {
                    /* Safari */
                    elem.webkitRequestFullscreen();
                } else if (elem.msRequestFullscreen) {
                    /* IE11 */
                    elem.msRequestFullscreen();
                }
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    /* Safari */
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    /* IE11 */
                    document.msExitFullscreen();
                }
            }
        }
    </script>

    <?php echo $__env->make('layouts.service-worker-js', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/trix/1.3.1/trix.min.js"></script>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Downloads\TableTrack v1.2.33 Nulled\TableTrack v1.2.33 Nulled\script\resources\views/layouts/app.blade.php ENDPATH**/ ?>