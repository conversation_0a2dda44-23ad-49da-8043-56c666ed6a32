9999999999O:35:"App\Models\SuperadminPaymentGateway":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:27:"superadmin_payment_gateways";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:52:{s:2:"id";i:1;s:13:"razorpay_type";s:4:"test";s:17:"test_razorpay_key";N;s:20:"test_razorpay_secret";N;s:25:"razorpay_test_webhook_key";N;s:17:"live_razorpay_key";N;s:20:"live_razorpay_secret";N;s:25:"razorpay_live_webhook_key";N;s:15:"razorpay_status";i:0;s:11:"stripe_type";s:4:"test";s:15:"test_stripe_key";N;s:18:"test_stripe_secret";N;s:23:"stripe_test_webhook_key";N;s:15:"live_stripe_key";N;s:18:"live_stripe_secret";N;s:23:"stripe_live_webhook_key";N;s:13:"stripe_status";i:0;s:10:"created_at";s:19:"2025-06-29 17:42:40";s:10:"updated_at";s:19:"2025-06-29 17:42:40";s:18:"flutterwave_status";i:0;s:16:"flutterwave_type";s:4:"test";s:20:"test_flutterwave_key";N;s:23:"test_flutterwave_secret";N;s:21:"test_flutterwave_hash";N;s:28:"flutterwave_test_webhook_key";N;s:20:"live_flutterwave_key";N;s:23:"live_flutterwave_secret";N;s:21:"live_flutterwave_hash";N;s:28:"flutterwave_live_webhook_key";N;s:21:"live_paypal_client_id";N;s:18:"live_paypal_secret";N;s:21:"test_paypal_client_id";N;s:18:"test_paypal_secret";N;s:13:"paypal_status";i:0;s:11:"paypal_mode";s:7:"sandbox";s:19:"payfast_merchant_id";N;s:20:"payfast_merchant_key";N;s:18:"payfast_passphrase";N;s:24:"test_payfast_merchant_id";N;s:25:"test_payfast_merchant_key";N;s:23:"test_payfast_passphrase";N;s:12:"payfast_mode";s:7:"sandbox";s:14:"payfast_status";i:0;s:17:"live_paystack_key";N;s:20:"live_paystack_secret";N;s:28:"live_paystack_merchant_email";N;s:17:"test_paystack_key";N;s:20:"test_paystack_secret";N;s:28:"test_paystack_merchant_email";N;s:20:"paystack_payment_url";s:23:"https://api.paystack.co";s:15:"paystack_status";i:0;s:13:"paystack_mode";s:7:"sandbox";}s:11:" * original";a:52:{s:2:"id";i:1;s:13:"razorpay_type";s:4:"test";s:17:"test_razorpay_key";N;s:20:"test_razorpay_secret";N;s:25:"razorpay_test_webhook_key";N;s:17:"live_razorpay_key";N;s:20:"live_razorpay_secret";N;s:25:"razorpay_live_webhook_key";N;s:15:"razorpay_status";i:0;s:11:"stripe_type";s:4:"test";s:15:"test_stripe_key";N;s:18:"test_stripe_secret";N;s:23:"stripe_test_webhook_key";N;s:15:"live_stripe_key";N;s:18:"live_stripe_secret";N;s:23:"stripe_live_webhook_key";N;s:13:"stripe_status";i:0;s:10:"created_at";s:19:"2025-06-29 17:42:40";s:10:"updated_at";s:19:"2025-06-29 17:42:40";s:18:"flutterwave_status";i:0;s:16:"flutterwave_type";s:4:"test";s:20:"test_flutterwave_key";N;s:23:"test_flutterwave_secret";N;s:21:"test_flutterwave_hash";N;s:28:"flutterwave_test_webhook_key";N;s:20:"live_flutterwave_key";N;s:23:"live_flutterwave_secret";N;s:21:"live_flutterwave_hash";N;s:28:"flutterwave_live_webhook_key";N;s:21:"live_paypal_client_id";N;s:18:"live_paypal_secret";N;s:21:"test_paypal_client_id";N;s:18:"test_paypal_secret";N;s:13:"paypal_status";i:0;s:11:"paypal_mode";s:7:"sandbox";s:19:"payfast_merchant_id";N;s:20:"payfast_merchant_key";N;s:18:"payfast_passphrase";N;s:24:"test_payfast_merchant_id";N;s:25:"test_payfast_merchant_key";N;s:23:"test_payfast_passphrase";N;s:12:"payfast_mode";s:7:"sandbox";s:14:"payfast_status";i:0;s:17:"live_paystack_key";N;s:20:"live_paystack_secret";N;s:28:"live_paystack_merchant_email";N;s:17:"test_paystack_key";N;s:20:"test_paystack_secret";N;s:28:"test_paystack_merchant_email";N;s:20:"paystack_payment_url";s:23:"https://api.paystack.co";s:15:"paystack_status";i:0;s:13:"paystack_mode";s:7:"sandbox";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}}