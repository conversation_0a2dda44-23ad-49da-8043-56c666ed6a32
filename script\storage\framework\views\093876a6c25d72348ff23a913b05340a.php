<li x-data="{ active: <?php if ((object) ('active') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('active'->value()); ?>')<?php echo e('active'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('active'); ?>')<?php endif; ?> }" x-init="if (active) { setTimeout(() => { $el.scrollIntoView({ behavior: 'smooth' }); }, 400); }">
    <a href="<?php echo e($link); ?>" wire:navigate
        class="<?php echo \Illuminate\Support\Arr::toCssClasses(['flex items-center p-2 text-base text-gray-600 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700', 'text-gray-900 font-bold' => $active]); ?>"><?php echo e($name); ?></a>
</li>
<?php /**PATH C:\Users\<USER>\Downloads\TableTrack v1.2.33 Nulled\TableTrack v1.2.33 Nulled\script\resources\views/livewire/sidebar-dropdown-menu.blade.php ENDPATH**/ ?>