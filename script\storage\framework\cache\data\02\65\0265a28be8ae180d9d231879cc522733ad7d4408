9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:26:"App\Models\LanguageSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:17:"language_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:13:"language_code";s:2:"en";s:13:"language_name";s:7:"English";s:9:"flag_code";s:2:"gb";s:6:"active";i:1;s:6:"is_rtl";i:0;s:10:"created_at";N;s:10:"updated_at";N;}s:11:" * original";a:8:{s:2:"id";i:1;s:13:"language_code";s:2:"en";s:13:"language_name";s:7:"English";s:9:"flag_code";s:2:"gb";s:6:"active";i:1;s:6:"is_rtl";i:0;s:10:"created_at";N;s:10:"updated_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}}}s:28:" * escapeWhenCastingToString";b:0;}